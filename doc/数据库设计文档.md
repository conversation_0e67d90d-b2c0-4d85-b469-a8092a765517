
# BBS 论坛系统数据库设计

## 一、ER 图

![ER图](ER图.png)

---

## 二、表结构设计

### 1. `user` 用户表

| 列名           | 类型                  | 说明             |
|----------------|-----------------------|-----------------|
| `id`           | BIGINT AUTO_INCREMENT | 主键             |
| `email`        | VARCHAR(255)          | 唯一，登录邮箱   |
| `password`     | VARCHAR(255)          | 加密密码         |
| `name`         | VARCHAR(100)          | 昵称             |
| `contact`      | VARCHAR(255)          | 联系方式         |
| `job_type`     | VARCHAR(50)           | 工作性质         |
| `job_location` | VARCHAR(100)          | 工作地点         |
| `created_at`   | DATETIME              | 注册时间         |
| `Is_admin`     | VARCHAR(50)           | 是否管理员       |
| `user_point`   | INT                   | 积分             |

---

### 2. `section` 板块表

| 列名         | 类型              | 说明               |
|--------------|-------------------|--------------------|
| `id`         | INT AUTO_INCREMENT| 主键               |
| `name`       | VARCHAR(100)      | 板块名称           |
| `description`| VARCHAR(255)      | 描述               |
| `created_at` | DATETIME          | 创建时间           |


### 3. `post` 帖子表

| 列名          | 类型                  | 说明                       |
|---------------|-----------------------|----------------------------|
| `id`          | BIGINT AUTO_INCREMENT | 主键                       |
| `user_id`     | BIGINT                | FK → user.id               |
| `section_id`  | INT                   | FK → section.id            |
| `title`       | VARCHAR(200)          | 标题，回帖可为空           |
| `content`     | TEXT                  | 正文                       |
| `is_help`     | TINYINT(1) DEFAULT 0  | 是否求助帖                 |
| `help_points` | INT DEFAULT 0         | 求助帖奖励积分             |
| `is_pinned`   | TINYINT(1) DEFAULT 0  | 是否置顶                   |
| `is_featured` | TINYINT(1) DEFAULT 0  | 是否加精                   |
| `created_at`  | DATETIME              | 创建时间                   |
| `updated_at`  | DATETIME              | 更新时间                   |
| `view_count`  | INT NOT NULL DEFAULT 0 | 浏览次数                   |
| `comment_count`| INT NOT NULL DEFAULT 0 | 回复数量                   |
| `like_count`  | INT NOT NULL DEFAULT 0 | 点赞数量                   |

---

### 4. `comments` 回复/评论表

| 字段名             | 数据类型                                   | 说明                                                                 |
|--------------------|------------------------------|---------------------|
| `comment_id`       | INT| 回复ID (主键, 自增)           |
| `post_id`          | INT| 所属帖子ID (外键, 关联 `post(id)`, 非空, 级联删除) |
| `user_id`          | INT| 回复用户ID (外键, 关联 `user(id)`, 非空) |
| `parent_comment_id`| INT| 父回复ID (外键, 关联 `comments(comment_id)`, 可空, 级联删除, 用于楼中楼) |
| `content`          | TEXT| 回复内容 |
| `created_at`       | DATETIME| 回复创建时间 (非空, 默认当前时间) |
| `upvotes`          | INT| 点赞数 (非空, 默认0)|

---