# 目录
[1.项目概述	2](#_toc196411959)

[2.功能需求	3](#_toc196411960)

[2.1 用户模块	3](#_toc196411961)

[2.2 帖子管理模块	4](#_toc196411962)

[2.3 管理员模块	5](#_toc196411963)

[2.4积分系统	6](#_toc196411964)




# <a name="_toc196411959"></a>1.项目概述
项目名称：

BBS论坛系统技术架构：SpringBoot3 + MySQL +vue3

核心目标：

提供用户交流平台，支持发帖、回帖、互动功能。

实现内容管理（置顶、加精、积分奖励）。


# <a name="_toc196411960"></a>2.功能需求
## <a name="_toc196411961"></a>2.1 用户模块

|功能|描述|
| - | - |
|用户注册|支持邮箱注册，密码加密存储。|
|个人资料维护|编辑联系方式、工作性质、工作地点等字段。 |
|权限管理|游客、普通用户、管理员。|



## <a name="_toc196411962"></a>2.2 帖子管理模块

|功能|描述|
| - | - |
|发布帖子|帖子结构，标题+正文+图片（可选）|
|回复帖子|楼层式回复，实现楼中楼|
|帖子置顶（分板块）|管理员权力|
|帖子加精|管理员权力|
|发布求助帖|设置积分奖励|


## <a name="_toc196411963"></a>2.3 管理员模块

|功能|描述|备注|
| - | - | - |
|管理员|可以设置或删除板块,可以对帖子进行修改|可以修改其他账户, 拥有积分奖励权限,拥有加精权限,拥有置顶权限|
|普通用户|可以发帖，回复，点赞等|帖子作者可以修改自己的帖子,可以设置是否”求助”(需要积分)|
|游客|只可浏览||


## <a name="_toc196411964"></a>2.4积分系统

|功能|描述|备注|
| - | - | - |
|积分获取|<p>每日签到+10</p><p>加精奖励+100</p><p>完成求助帖（由求助帖发帖人设置）</p>|<p>连续每日签到额外奖励</p><p>完成注册给予50</p>|
|积分消耗|<p>违规扣除</p><p>发布求助帖</p>|求助帖设置下限50积分|
|积分商城|可兑换称号，昵称颜色等||

