package cn.byssted.bbs.bbsrd.controller;

import cn.byssted.bbs.bbsrd.common.Result;
import cn.byssted.bbs.bbsrd.dto.PostCreateDTO;
import cn.byssted.bbs.bbsrd.entity.Post;
import cn.byssted.bbs.bbsrd.service.PostService;
import cn.byssted.bbs.bbsrd.util.JwtUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 帖子控制器
 */
@RestController
@RequestMapping("/api/posts")
@CrossOrigin(origins = "*")
public class PostController {
    
    @Autowired
    private PostService postService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 获取帖子列表
     */
    @GetMapping
    public Result<IPage<Post>> getPostList(@RequestParam(defaultValue = "1") int page,
                                          @RequestParam(defaultValue = "10") int size,
                                          @RequestParam(required = false) Integer sectionId) {
        try {
            IPage<Post> postList = postService.getPostList(page, size, sectionId);
            return Result.success(postList);
        } catch (Exception e) {
            return Result.error("获取帖子列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取帖子详情
     */
    @GetMapping("/{id}")
    public Result<Post> getPostById(@PathVariable Long id) {
        try {
            Post post = postService.getPostById(id);
            if (post == null) {
                return Result.notFound("帖子不存在");
            }
            return Result.success(post);
        } catch (Exception e) {
            return Result.error("获取帖子详情失败：" + e.getMessage());
        }
    }
    
    /**
     * 发布帖子
     */
    @PostMapping
    public Result<Post> createPost(@RequestHeader("Authorization") String token,
                                  @RequestBody PostCreateDTO postCreateDTO) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return Result.unauthorized("请先登录");
            }
            
            String jwtToken = token.substring(7);
            Long userId = jwtUtil.getUserIdFromToken(jwtToken);
            
            // 参数验证
            if (postCreateDTO.getTitle() == null || postCreateDTO.getTitle().trim().isEmpty()) {
                return Result.badRequest("标题不能为空");
            }
            if (postCreateDTO.getContent() == null || postCreateDTO.getContent().trim().isEmpty()) {
                return Result.badRequest("内容不能为空");
            }
            if (postCreateDTO.getSectionId() == null) {
                return Result.badRequest("板块不能为空");
            }
            
            Post post = postService.createPost(postCreateDTO, userId);
            return Result.success("发布成功", post);
        } catch (Exception e) {
            return Result.error("发布帖子失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新帖子
     */
    @PutMapping("/{id}")
    public Result<String> updatePost(@RequestHeader("Authorization") String token,
                                    @PathVariable Long id,
                                    @RequestBody PostCreateDTO postCreateDTO) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return Result.unauthorized("请先登录");
            }
            
            String jwtToken = token.substring(7);
            Long userId = jwtUtil.getUserIdFromToken(jwtToken);
            
            Post post = postService.getPostById(id);
            if (post == null) {
                return Result.notFound("帖子不存在");
            }
            
            // 只有作者可以修改帖子
            if (!post.getUserId().equals(userId)) {
                return Result.forbidden("无权限修改此帖子");
            }
            
            // 更新帖子信息
            if (postCreateDTO.getTitle() != null) {
                post.setTitle(postCreateDTO.getTitle());
            }
            if (postCreateDTO.getContent() != null) {
                post.setContent(postCreateDTO.getContent());
            }
            if (postCreateDTO.getSectionId() != null) {
                post.setSectionId(postCreateDTO.getSectionId());
            }
            
            boolean success = postService.updatePost(post);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            return Result.error("更新帖子失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除帖子
     */
    @DeleteMapping("/{id}")
    public Result<String> deletePost(@RequestHeader("Authorization") String token,
                                    @PathVariable Long id) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return Result.unauthorized("请先登录");
            }
            
            String jwtToken = token.substring(7);
            Long userId = jwtUtil.getUserIdFromToken(jwtToken);
            
            boolean success = postService.deletePost(id, userId);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败或无权限");
            }
        } catch (Exception e) {
            return Result.error("删除帖子失败：" + e.getMessage());
        }
    }
    
    /**
     * 点赞帖子
     */
    @PostMapping("/{id}/like")
    public Result<String> likePost(@RequestHeader("Authorization") String token,
                                  @PathVariable Long id) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return Result.unauthorized("请先登录");
            }
            
            boolean success = postService.likePost(id);
            if (success) {
                return Result.success("点赞成功");
            } else {
                return Result.error("点赞失败");
            }
        } catch (Exception e) {
            return Result.error("点赞失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户发布的帖子
     */
    @GetMapping("/user/{userId}")
    public Result<IPage<Post>> getUserPosts(@PathVariable Long userId,
                                           @RequestParam(defaultValue = "1") int page,
                                           @RequestParam(defaultValue = "10") int size) {
        try {
            IPage<Post> postList = postService.getUserPosts(userId, page, size);
            return Result.success(postList);
        } catch (Exception e) {
            return Result.error("获取用户帖子失败：" + e.getMessage());
        }
    }
}
