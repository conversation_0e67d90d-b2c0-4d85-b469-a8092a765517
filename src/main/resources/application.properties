# ????
spring.application.name=bbs-RD
server.port=8080

# ?????
spring.datasource.url=***************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=S2pAR8m3MmX54iFL
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# MyBatis Plus ??
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0

# JWT ??
jwt.secret=bbs-forum-secret-key-2024
jwt.expiration=86400000

# ??????
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# ????
logging.level.cn.byssted.bbs.bbsrd=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
